import type { Schema, Struct } from '@strapi/strapi';

export interface AdminApiToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_tokens';
  info: {
    description: '';
    displayName: 'Api Token';
    name: 'Api Token';
    pluralName: 'api-tokens';
    singularName: 'api-token';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<''>;
    expiresAt: Schema.Attribute.DateTime;
    lastUsedAt: Schema.Attribute.DateTime;
    lifespan: Schema.Attribute.BigInteger;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::api-token'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<'oneToMany', 'admin::api-token-permission'>;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'read-only'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_token_permissions';
  info: {
    description: '';
    displayName: 'API Token Permission';
    name: 'API Token Permission';
    pluralName: 'api-token-permissions';
    singularName: 'api-token-permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::api-token-permission'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    token: Schema.Attribute.Relation<'manyToOne', 'admin::api-token'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface AdminPermission extends Struct.CollectionTypeSchema {
  collectionName: 'admin_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'Permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
    conditions: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::permission'> &
      Schema.Attribute.Private;
    properties: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<'manyToOne', 'admin::role'>;
    subject: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface AdminRole extends Struct.CollectionTypeSchema {
  collectionName: 'admin_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'Role';
    pluralName: 'roles';
    singularName: 'role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::role'> & Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<'oneToMany', 'admin::permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    users: Schema.Attribute.Relation<'manyToMany', 'admin::user'>;
  };
}

export interface AdminTransferToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_tokens';
  info: {
    description: '';
    displayName: 'Transfer Token';
    name: 'Transfer Token';
    pluralName: 'transfer-tokens';
    singularName: 'transfer-token';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<''>;
    expiresAt: Schema.Attribute.DateTime;
    lastUsedAt: Schema.Attribute.DateTime;
    lifespan: Schema.Attribute.BigInteger;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::transfer-token'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<'oneToMany', 'admin::transfer-token-permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface AdminTransferTokenPermission extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    description: '';
    displayName: 'Transfer Token Permission';
    name: 'Transfer Token Permission';
    pluralName: 'transfer-token-permissions';
    singularName: 'transfer-token-permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::transfer-token-permission'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    token: Schema.Attribute.Relation<'manyToOne', 'admin::transfer-token'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface AdminUser extends Struct.CollectionTypeSchema {
  collectionName: 'admin_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'User';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    blocked: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    firstname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    isActive: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>;
    lastname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::user'> & Schema.Attribute.Private;
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    preferedLanguage: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    registrationToken: Schema.Attribute.String & Schema.Attribute.Private;
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
    roles: Schema.Attribute.Relation<'manyToMany', 'admin::role'> & Schema.Attribute.Private;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    username: Schema.Attribute.String;
  };
}

export interface ApiAboutAbout extends Struct.SingleTypeSchema {
  collectionName: 'abouts';
  info: {
    description: 'Write about yourself and the content you create';
    displayName: 'About';
    pluralName: 'abouts';
    singularName: 'about';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    blocks: Schema.Attribute.DynamicZone<
      ['shared.media', 'shared.quote', 'shared.rich-text', 'shared.slider']
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::about.about'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiAffiliateAffiliate extends Struct.CollectionTypeSchema {
  collectionName: 'affiliates';
  info: {
    description: '';
    displayName: 'Affiliate';
    pluralName: 'affiliates';
    singularName: 'affiliate';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    airtable_data: Schema.Attribute.Component<'shared.airtable-config', false>;
    avg_conversion: Schema.Attribute.Decimal;
    avg_price: Schema.Attribute.Decimal;
    brand_keywords_tiktok: Schema.Attribute.Text;
    brand_keywords_youtube: Schema.Attribute.Text;
    categories: Schema.Attribute.Relation<'manyToMany', 'api::category.category'>;
    commission: Schema.Attribute.Relation<'manyToOne', 'api::commission.commission'>;
    commission_detail: Schema.Attribute.Blocks;
    company_name: Schema.Attribute.String;
    contact_information: Schema.Attribute.String;
    cookies_duration: Schema.Attribute.String;
    country: Schema.Attribute.Enumeration<
      [
        'Afghanistan (AF)',
        'Albania (AL)',
        'Algeria (DZ)',
        'Andorra (AD)',
        'Angola (AO)',
        'Antigua and Barbuda (AG)',
        'Argentina (AR)',
        'Armenia (AM)',
        'Australia (AU)',
        'Austria (AT)',
        'Azerbaijan (AZ)',
        'Bahamas (BS)',
        'Bahrain (BH)',
        'Bangladesh (BD)',
        'Barbados (BB)',
        'Belarus (BY)',
        'Belgium (BE)',
        'Belize (BZ)',
        'Benin (BJ)',
        'Bhutan (BT)',
        'Bolivia (BO)',
        'Bosnia and Herzegovina (BA)',
        'Botswana (BW)',
        'Brazil (BR)',
        'Brunei (BN)',
        'Bulgaria (BG)',
        'Burkina Faso (BF)',
        'Burundi (BI)',
        'Cambodia (KH)',
        'Cameroon (CM)',
        'Canada (CA)',
        'Central African Republic (CF)',
        'Chad (TD)',
        'Chile (CL)',
        'China (CN)',
        'Colombia (CO)',
        'Comoros (KM)',
        'Congo (CG)',
        'Congo (DRC) (CD)',
        'Costa Rica (CR)',
        "C\u00F4te d'Ivoire (CI)",
        'Croatia (HR)',
        'Cuba (CU)',
        'Cyprus (CY)',
        'Czech Republic (CZ)',
        'Denmark (DK)',
        'Djibouti (DJ)',
        'Dominica (DM)',
        'Dominican Republic (DO)',
        'Ecuador (EC)',
        'Egypt (EG)',
        'El Salvador (SV)',
        'Equatorial Guinea (GQ)',
        'Eritrea (ER)',
        'Estonia (EE)',
        'Ethiopia (ET)',
        'Fiji (FJ)',
        'Finland (FI)',
        'France (FR)',
        'Gabon (GA)',
        'Gambia (GM)',
        'Georgia (GE)',
        'Germany (DE)',
        'Ghana (GH)',
        'Greece (GR)',
        'Grenada (GD)',
        'Guatemala (GT)',
        'Guinea (GN)',
        'Guinea-Bissau (GW)',
        'Guyana (GY)',
        'Haiti (HT)',
        'Honduras (HN)',
        'Hungary (HU)',
        'Iceland (IS)',
        'India (IN)',
        'Indonesia (ID)',
        'Iran (IR)',
        'Iraq (IQ)',
        'Ireland (IE)',
        'Israel (IL)',
        'Italy (IT)',
        'Jamaica (JM)',
        'Japan (JP)',
        'Jordan (JO)',
        'Kazakhstan (KZ)',
        'Kenya (KE)',
        'Kiribati (KI)',
        'North Korea (KP)',
        'South Korea (KR)',
        'Kosovo (XK)',
        'Kuwait (KW)',
        'Kyrgyzstan (KG)',
        'Laos (LA)',
        'Latvia (LV)',
        'Lebanon (LB)',
        'Lesotho (LS)',
        'Liberia (LR)',
        'Libya (LY)',
        'Lithuania (LT)',
        'Luxembourg (LU)',
        'Macedonia (MK)',
        'Madagascar (MG)',
        'Malawi (MW)',
        'Malaysia (MY)',
        'Maldives (MV)',
        'Mali (ML)',
        'Malta (MT)',
        'Marshall Islands (MH)',
        'Mauritania (MR)',
        'Mauritius (MU)',
        'Mexico (MX)',
        'Micronesia (FM)',
        'Moldova (MD)',
        'Monaco (MC)',
        'Mongolia (MN)',
        'Montenegro (ME)',
        'Morocco (MA)',
        'Mozambique (MZ)',
        'Myanmar (MM)',
        'Namibia (NA)',
        'Nauru (NR)',
        'Nepal (NP)',
        'Netherlands (NL)',
        'New Zealand (NZ)',
        'Nicaragua (NI)',
        'Niger (NE)',
        'Nigeria (NG)',
        'Norway (NO)',
        'Oman (OM)',
        'Pakistan (PK)',
        'Palau (PW)',
        'Panama (PA)',
        'Papua New Guinea (PG)',
        'Paraguay (PY)',
        'Peru (PE)',
        'Philippines (PH)',
        'Poland (PL)',
        'Portugal (PT)',
        'Qatar (QA)',
        'Romania (RO)',
        'Russia (RU)',
        'Rwanda (RW)',
        'Saint Kitts and Nevis (KN)',
        'Saint Lucia (LC)',
        'Saint Vincent and the Grenadines (VC)',
        'Samoa (WS)',
        'San Marino (SM)',
        'Sao Tome and Principe (ST)',
        'Saudi Arabia (SA)',
        'Senegal (SN)',
        'Serbia (RS)',
        'Seychelles (SC)',
        'Sierra Leone (SL)',
        'Singapore (SG)',
        'Sint Maarten (SX)',
        'Slovakia (SK)',
        'Slovenia (SI)',
        'Solomon Islands (SB)',
        'Somalia (SO)',
        'South Africa (ZA)',
        'South Sudan (SS)',
        'Spain (ES)',
        'Sri Lanka (LK)',
        'Sudan (SD)',
        'Suriname (SR)',
        'Sweden (SE)',
        'Switzerland (CH)',
        'Syria (SY)',
        'Tajikistan (TJ)',
        'Tanzania (TZ)',
        'Thailand (TH)',
        'Timor-Leste (TL)',
        'Togo (TG)',
        'Tonga (TO)',
        'Trinidad and Tobago (TT)',
        'Tunisia (TN)',
        'Turkey (TR)',
        'Turkmenistan (TM)',
        'Tuvalu (TV)',
        'Uganda (UG)',
        'Ukraine (UA)',
        'United Arab Emirates (AE)',
        'United Kingdom (GB)',
        'United States (US)',
        'Uruguay (UY)',
        'Uzbekistan (UZ)',
        'Vanuatu (VU)',
        'Vatican City (VA)',
        'Venezuela (VE)',
        'Viet Nam (VN)',
        'Yemen (YE)',
        'Zambia (ZM)',
        'Zimbabwe (ZW)',
      ]
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    currency: Schema.Attribute.Enumeration<['USD', 'EUR']> & Schema.Attribute.DefaultTo<'USD'>;
    detail: Schema.Attribute.Blocks;
    domain: Schema.Attribute.String;
    features: Schema.Attribute.Text & Schema.Attribute.DefaultTo<'feature1, feature2,'>;
    from_airtable: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    industry: Schema.Attribute.Relation<'manyToOne', 'api::industry.industry'>;
    launch_year: Schema.Attribute.Integer;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::affiliate.affiliate'> &
      Schema.Attribute.Private;
    minimum_payout: Schema.Attribute.Decimal;
    monthly_traffic: Schema.Attribute.BigInteger;
    name: Schema.Attribute.String & Schema.Attribute.Required & Schema.Attribute.Unique;
    payment_methods: Schema.Attribute.Relation<'manyToMany', 'api::payment-method.payment-method'>;
    pricing: Schema.Attribute.String;
    pricing_range: Schema.Attribute.Component<'shared.pricing-range', false>;
    publishedAt: Schema.Attribute.DateTime;
    recurring: Schema.Attribute.Enumeration<
      [
        'One time',
        'Life time',
        'In 1 month',
        'In 2 months',
        'In 3 months',
        'In 4 months',
        'In 5 months',
        'In 6 months',
        'In 9 months',
        'In 12 months',
        'In 15 months',
        'In 18 months',
        'In 24 months',
        'In 36 months',
      ]
    >;
    recurring_priority: Schema.Attribute.Integer;
    slug: Schema.Attribute.String & Schema.Attribute.Unique;
    social_logs: Schema.Attribute.Relation<'oneToMany', 'api::social-log.social-log'>;
    tag_line: Schema.Attribute.String;
    tags: Schema.Attribute.Relation<'manyToMany', 'api::tag.tag'>;
    tiktok_ad_keyword: Schema.Attribute.String;
    traffic_rank: Schema.Attribute.Integer;
    traffic_webs: Schema.Attribute.Relation<'oneToMany', 'api::traffic-web.traffic-web'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    url: Schema.Attribute.String;
    user_submitted: Schema.Attribute.Relation<'oneToOne', 'plugin::users-permissions.user'>;
    youtube_ad_keyword: Schema.Attribute.String;
  };
}

export interface ApiAirtableAirtable extends Struct.CollectionTypeSchema {
  collectionName: 'airtables';
  info: {
    description: 'Configure the Airtable integration settings';
    displayName: 'Airtable Integration';
    pluralName: 'airtables';
    singularName: 'airtable';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    last_sync_time: Schema.Attribute.DateTime;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::airtable.airtable'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    sync_count: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
    sync_message: Schema.Attribute.String;
    sync_status: Schema.Attribute.Enumeration<['fail', 'success']> &
      Schema.Attribute.DefaultTo<'success'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiAiscriptSessionAiscriptSession extends Struct.CollectionTypeSchema {
  collectionName: 'aiscript_sessions';
  info: {
    description: 'Stores AI script session data';
    displayName: 'AI Script Session';
    pluralName: 'aiscript-sessions';
    singularName: 'aiscript-session';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    aiscripts: Schema.Attribute.Relation<'oneToMany', 'api::aiscript.aiscript'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    end_time: Schema.Attribute.DateTime;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::aiscript-session.aiscript-session'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    session_id: Schema.Attribute.String & Schema.Attribute.Required & Schema.Attribute.Unique;
    session_status: Schema.Attribute.Enumeration<['active', 'ended']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'active'>;
    start_time: Schema.Attribute.DateTime & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    users_permissions_user: Schema.Attribute.Relation<'oneToOne', 'plugin::users-permissions.user'>;
  };
}

export interface ApiAiscriptAiscript extends Struct.CollectionTypeSchema {
  collectionName: 'aiscripts';
  info: {
    description: 'Stores AI script data and execution history';
    displayName: 'AI Script';
    pluralName: 'aiscripts';
    singularName: 'aiscript';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    aiscript_session: Schema.Attribute.Relation<
      'manyToOne',
      'api::aiscript-session.aiscript-session'
    >;
    content: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    input: Schema.Attribute.Text;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::aiscript.aiscript'> &
      Schema.Attribute.Private;
    model_version: Schema.Attribute.String;
    output: Schema.Attribute.Text;
    parameters: Schema.Attribute.JSON;
    publishedAt: Schema.Attribute.DateTime;
    title: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiCategoryCategory extends Struct.CollectionTypeSchema {
  collectionName: 'categories';
  info: {
    description: 'Organize your content into categories';
    displayName: 'Category';
    pluralName: 'categories';
    singularName: 'category';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    affiliates: Schema.Attribute.Relation<'manyToMany', 'api::affiliate.affiliate'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    description: Schema.Attribute.Text;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::category.category'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.UID;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiChatMessageChatMessage extends Struct.CollectionTypeSchema {
  collectionName: 'chat_messages';
  info: {
    description: 'Stores chat message data';
    displayName: 'Chat Message';
    pluralName: 'chat-messages';
    singularName: 'chat-message';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    content: Schema.Attribute.Text & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    direction: Schema.Attribute.Enumeration<['incoming', 'outgoing']> & Schema.Attribute.Required;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::chat-message.chat-message'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    session: Schema.Attribute.Relation<'manyToOne', 'api::chat-session.chat-session'>;
    timestamp: Schema.Attribute.DateTime &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'now'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiChatSessionChatSession extends Struct.CollectionTypeSchema {
  collectionName: 'chat_sessions';
  info: {
    description: 'Stores chat session data';
    displayName: 'Chat Session';
    pluralName: 'chat-sessions';
    singularName: 'chat-session';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    end_time: Schema.Attribute.DateTime;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::chat-session.chat-session'> &
      Schema.Attribute.Private;
    messages: Schema.Attribute.Relation<'oneToMany', 'api::chat-message.chat-message'>;
    publishedAt: Schema.Attribute.DateTime;
    session_id: Schema.Attribute.String & Schema.Attribute.Required & Schema.Attribute.Unique;
    session_status: Schema.Attribute.Enumeration<['active', 'ended']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'active'>;
    start_time: Schema.Attribute.DateTime & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    users_permissions_user: Schema.Attribute.Relation<'oneToOne', 'plugin::users-permissions.user'>;
  };
}

export interface ApiCommissionConfigCommissionConfig extends Struct.SingleTypeSchema {
  collectionName: 'commission_configs';
  info: {
    description: '';
    displayName: 'Commission Config';
    pluralName: 'commission-configs';
    singularName: 'commission-config';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    cookie_duration: Schema.Attribute.Integer;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    default_commission_percentage: Schema.Attribute.Decimal & Schema.Attribute.DefaultTo<0>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::commission-config.commission-config'
    > &
      Schema.Attribute.Private;
    minimum_payout: Schema.Attribute.Decimal & Schema.Attribute.DefaultTo<50>;
    payout_cycle: Schema.Attribute.Enumeration<['weekly', 'biweekly', 'monthly']>;
    premium_tier_names: Schema.Attribute.Text;
    premium_tier_percentage: Schema.Attribute.Decimal;
    pro_tier_names: Schema.Attribute.Text;
    pro_tier_percentage: Schema.Attribute.Decimal;
    processing_fee: Schema.Attribute.Decimal;
    publishedAt: Schema.Attribute.DateTime;
    reverse_rate: Schema.Attribute.Integer;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiCommissionCommission extends Struct.CollectionTypeSchema {
  collectionName: 'commissions';
  info: {
    description: '';
    displayName: 'Commission';
    pluralName: 'commissions';
    singularName: 'commission';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    affiliates: Schema.Attribute.Relation<'oneToMany', 'api::affiliate.affiliate'>;
    avg_commission: Schema.Attribute.Decimal;
    commission_detail: Schema.Attribute.Blocks;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::commission.commission'> &
      Schema.Attribute.Private;
    max_percentage: Schema.Attribute.Decimal;
    note: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    title: Schema.Attribute.String;
    type: Schema.Attribute.Enumeration<['fix_price', 'fix_percentage', 'range_percentage']>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    value_from: Schema.Attribute.Decimal & Schema.Attribute.DefaultTo<0>;
    value_to: Schema.Attribute.Decimal & Schema.Attribute.DefaultTo<0>;
  };
}

export interface ApiGlobalGlobal extends Struct.SingleTypeSchema {
  collectionName: 'globals';
  info: {
    description: 'Define global settings';
    displayName: 'Global';
    pluralName: 'globals';
    singularName: 'global';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    aiModel: Schema.Attribute.Enumeration<
      [
        'gpt-4o',
        'claude-3-sonnet-3.7',
        'gemini-1.5-pro-002',
        'gemini-2.0-flash-lite',
        'gemini-1.5-flash',
        'gemini-2.0-flash',
        'n8n-gpt',
        'n8n-gemini',
      ]
    >;
    airtable_base_id: Schema.Attribute.String;
    airtable_sync_schedule: Schema.Attribute.Enumeration<
      [
        'Daily (midnight)',
        'Every minute',
        'Every 5 minutes',
        'Every 10 minutes',
        'Every 30 minutes',
        'Hourly',
      ]
    > &
      Schema.Attribute.DefaultTo<'Daily (midnight)'>;
    airtable_table_id: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    default_free_requests: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<100>;
    default_user_ref_rate: Schema.Attribute.Decimal;
    defaultPrompt: Schema.Attribute.Text;
    defaultSeo: Schema.Attribute.Component<'shared.seo', false>;
    favicon: Schema.Attribute.Media<'images' | 'files' | 'videos'>;
    fetch_ads_schedule: Schema.Attribute.Enumeration<
      [
        'Daily (midnight)',
        'Every 2 minutes',
        'Every 5 minutes',
        'Every 10 minutes',
        'Every 15 minutes',
        'Every 30 minutes',
        'Every hour',
        'Every 4 hours',
        'Every 8 hours',
        'Every 12 hours',
        'Weekly',
      ]
    > &
      Schema.Attribute.DefaultTo<'Daily (midnight)'>;
    ignore_tiktok_des: Schema.Attribute.Text;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::global.global'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    refresh_social_after: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<300>;
    refresh_tiktok_interval: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<300>;
    refresh_youtube_interval: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<600>;
    resolved_affiliates: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
    siteDescription: Schema.Attribute.Text & Schema.Attribute.Required;
    siteName: Schema.Attribute.String & Schema.Attribute.Required;
    slack_webhook_url: Schema.Attribute.String;
    summaryPrompt: Schema.Attribute.Text;
    tiktok_thumbnail_update_schedule: Schema.Attribute.Enumeration<
      [
        'Daily (midnight)',
        'Every 5 minutes',
        'Every 2 hours',
        'Every 4 hours',
        'Every 3 days',
        'Weekly',
        'Every 2 weeks',
        'Monthly',
      ]
    > &
      Schema.Attribute.DefaultTo<'Every 3 days'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiIndustryIndustry extends Struct.CollectionTypeSchema {
  collectionName: 'industries';
  info: {
    displayName: 'Industry';
    pluralName: 'industries';
    singularName: 'industry';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    affiliates: Schema.Attribute.Relation<'oneToMany', 'api::affiliate.affiliate'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::industry.industry'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiKeywordKeyword extends Struct.CollectionTypeSchema {
  collectionName: 'keywords';
  info: {
    displayName: 'Keywords';
    pluralName: 'keywords';
    singularName: 'keyword';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    keyword: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::keyword.keyword'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    rank: Schema.Attribute.Integer;
    traffic: Schema.Attribute.Integer;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiPagePage extends Struct.CollectionTypeSchema {
  collectionName: 'pages';
  info: {
    description: 'User-generated pages with rich text content';
    displayName: 'User Page';
    pluralName: 'pages';
    singularName: 'page';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    author: Schema.Attribute.Relation<'manyToOne', 'plugin::users-permissions.user'>;
    content: Schema.Attribute.JSON;
    content_html: Schema.Attribute.Text;
    content_plain: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    excerpt: Schema.Attribute.Text &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 500;
      }>;
    featured_image: Schema.Attribute.Media<'images'>;
    last_edited_at: Schema.Attribute.DateTime;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::page.page'> &
      Schema.Attribute.Private;
    meta_description: Schema.Attribute.Text &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 160;
      }>;
    meta_title: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 60;
      }>;
    publishedAt: Schema.Attribute.DateTime;
    referrer_link: Schema.Attribute.Relation<'oneToOne', 'api::referrer-link.referrer-link'>;
    slug: Schema.Attribute.UID<'title'> & Schema.Attribute.Required;
    status: Schema.Attribute.Enumeration<['draft', 'published', 'archived']> &
      Schema.Attribute.DefaultTo<'draft'>;
    tags: Schema.Attribute.JSON;
    title: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 255;
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    view_count: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
  };
}

export interface ApiPaymentMethodPaymentMethod extends Struct.CollectionTypeSchema {
  collectionName: 'payment_methods';
  info: {
    displayName: 'Payment Method';
    pluralName: 'payment-methods';
    singularName: 'payment-method';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    affiliates: Schema.Attribute.Relation<'manyToMany', 'api::affiliate.affiliate'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::payment-method.payment-method'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiPayoutPayout extends Struct.CollectionTypeSchema {
  collectionName: 'payouts';
  info: {
    description: '';
    displayName: 'Payout';
    pluralName: 'payouts';
    singularName: 'payout';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    amount: Schema.Attribute.Decimal;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::payout.payout'> &
      Schema.Attribute.Private;
    method: Schema.Attribute.Enumeration<['paypal', 'bank transfer']>;
    payout_date: Schema.Attribute.DateTime;
    payout_status: Schema.Attribute.Enumeration<['pending', 'approved', 'completed']>;
    processing_fee: Schema.Attribute.Decimal;
    publishedAt: Schema.Attribute.DateTime;
    referrer: Schema.Attribute.Relation<'manyToOne', 'api::referrer.referrer'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiPromptPrompt extends Struct.CollectionTypeSchema {
  collectionName: 'prompts';
  info: {
    description: 'Reusable prompts for AI interactions';
    displayName: 'AI Prompt';
    pluralName: 'prompts';
    singularName: 'prompt';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    content: Schema.Attribute.Text & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    description: Schema.Attribute.Text;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::prompt.prompt'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    title: Schema.Attribute.String & Schema.Attribute.Required & Schema.Attribute.Unique;
    type: Schema.Attribute.Enumeration<['summary', 'aiscript']> &
      Schema.Attribute.DefaultTo<'aiscript'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiReferralActivityReferralActivity extends Struct.CollectionTypeSchema {
  collectionName: 'referral_activities';
  info: {
    description: '';
    displayName: 'Referral Activity';
    pluralName: 'referral-activities';
    singularName: 'referral-activity';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    amount_paid: Schema.Attribute.Decimal;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::referral-activity.referral-activity'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    referral: Schema.Attribute.Relation<'manyToOne', 'api::referral.referral'>;
    referral_status: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiReferralCommissionReferralCommission extends Struct.CollectionTypeSchema {
  collectionName: 'referral_commissions';
  info: {
    description: '';
    displayName: 'Referral Commission';
    pluralName: 'referral-commissions';
    singularName: 'referral-commission';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    commission_amount: Schema.Attribute.Decimal;
    commission_percentage: Schema.Attribute.Decimal;
    commission_status: Schema.Attribute.Enumeration<['ready', 'pending', 'paid']>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    gross_sale_amount: Schema.Attribute.Decimal;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::referral-commission.referral-commission'
    > &
      Schema.Attribute.Private;
    payment_date: Schema.Attribute.Date;
    publishedAt: Schema.Attribute.DateTime;
    referral: Schema.Attribute.Relation<'manyToOne', 'api::referral.referral'>;
    referrer: Schema.Attribute.Relation<'manyToOne', 'api::referrer.referrer'>;
    review_due_date: Schema.Attribute.Date;
    subscription_tier: Schema.Attribute.Relation<
      'oneToOne',
      'api::subscription-tier.subscription-tier'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiReferralReferral extends Struct.CollectionTypeSchema {
  collectionName: 'referrals';
  info: {
    description: '';
    displayName: 'Referral';
    pluralName: 'referrals';
    singularName: 'referral';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::referral.referral'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    referral_activities: Schema.Attribute.Relation<
      'oneToMany',
      'api::referral-activity.referral-activity'
    >;
    referral_commissions: Schema.Attribute.Relation<
      'oneToMany',
      'api::referral-commission.referral-commission'
    >;
    referral_status: Schema.Attribute.Enumeration<['cross', 'lead', 'conversion']>;
    referrer: Schema.Attribute.Relation<'manyToOne', 'api::referrer.referrer'>;
    referrer_link: Schema.Attribute.Relation<'manyToOne', 'api::referrer-link.referrer-link'>;
    total_paid: Schema.Attribute.Decimal & Schema.Attribute.DefaultTo<0>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    user: Schema.Attribute.Relation<'oneToOne', 'plugin::users-permissions.user'>;
  };
}

export interface ApiReferrerLinkReferrerLink extends Struct.CollectionTypeSchema {
  collectionName: 'referrer_links';
  info: {
    description: '';
    displayName: 'Referrer Links';
    pluralName: 'referrer-links';
    singularName: 'referrer-link';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    conversions: Schema.Attribute.Integer;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    direct_page_views: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
    leads: Schema.Attribute.Integer;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::referrer-link.referrer-link'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    page: Schema.Attribute.Relation<'oneToOne', 'api::page.page'>;
    publishedAt: Schema.Attribute.DateTime;
    referrals: Schema.Attribute.Relation<'oneToMany', 'api::referral.referral'>;
    referrer: Schema.Attribute.Relation<'manyToOne', 'api::referrer.referrer'>;
    referrer_link_views: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
    referrer_sources: Schema.Attribute.JSON;
    short_link: Schema.Attribute.String & Schema.Attribute.Unique;
    short_link_views: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
    track_links: Schema.Attribute.Relation<'oneToMany', 'api::track-link.track-link'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    url: Schema.Attribute.String;
    user: Schema.Attribute.Relation<'oneToOne', 'plugin::users-permissions.user'>;
    visitors: Schema.Attribute.Integer;
  };
}

export interface ApiReferrerReferrer extends Struct.CollectionTypeSchema {
  collectionName: 'referrers';
  info: {
    description: '';
    displayName: 'Referrer';
    pluralName: 'referrers';
    singularName: 'referrer';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    balance: Schema.Attribute.Decimal & Schema.Attribute.DefaultTo<0>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    custom_commission_percentage: Schema.Attribute.Decimal;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::referrer.referrer'> &
      Schema.Attribute.Private;
    payouts: Schema.Attribute.Relation<'oneToMany', 'api::payout.payout'>;
    publishedAt: Schema.Attribute.DateTime;
    referral_code: Schema.Attribute.String & Schema.Attribute.Unique;
    referral_commissions: Schema.Attribute.Relation<
      'oneToMany',
      'api::referral-commission.referral-commission'
    >;
    referrals: Schema.Attribute.Relation<'oneToMany', 'api::referral.referral'>;
    referrer_links: Schema.Attribute.Relation<'oneToMany', 'api::referrer-link.referrer-link'>;
    referrer_status: Schema.Attribute.Enumeration<['active', 'blocked']> &
      Schema.Attribute.DefaultTo<'active'>;
    total_earnings: Schema.Attribute.Decimal & Schema.Attribute.DefaultTo<0>;
    totalClicks: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
    totalConversions: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
    totalLeads: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    user: Schema.Attribute.Relation<'oneToOne', 'plugin::users-permissions.user'>;
  };
}

export interface ApiSearchKeywordSearchKeyword extends Struct.CollectionTypeSchema {
  collectionName: 'search_keywords';
  info: {
    displayName: 'Search Keyword';
    pluralName: 'search-keywords';
    singularName: 'search-keyword';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    count: Schema.Attribute.Integer;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    keyword: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::search-keyword.search-keyword'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiSocialListeningSocialListening extends Struct.CollectionTypeSchema {
  collectionName: 'social_listenings';
  info: {
    description: '';
    displayName: 'Social Listening';
    pluralName: 'social-listenings';
    singularName: 'social-listening';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    affiliate: Schema.Attribute.Relation<'oneToOne', 'api::affiliate.affiliate'>;
    channel_avatar: Schema.Attribute.Text;
    channel_id: Schema.Attribute.String;
    channel_title: Schema.Attribute.String;
    comments: Schema.Attribute.Integer;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    description: Schema.Attribute.Text;
    duration: Schema.Attribute.String;
    is_displayed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
    is_from_crawler: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    is_verified: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    keyword: Schema.Attribute.String;
    likes: Schema.Attribute.Integer;
    link: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::social-listening.social-listening'
    > &
      Schema.Attribute.Private;
    photos: Schema.Attribute.String;
    platform: Schema.Attribute.Enumeration<['youtube', 'tiktok', 'reddit', 'x']>;
    post_id: Schema.Attribute.String & Schema.Attribute.Unique;
    published_from: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    shares: Schema.Attribute.Integer;
    thumbnail: Schema.Attribute.Text;
    title: Schema.Attribute.Text;
    transcript: Schema.Attribute.Text;
    type: Schema.Attribute.Enumeration<['video', 'post']>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    video_id: Schema.Attribute.String & Schema.Attribute.Unique;
    video_link: Schema.Attribute.String;
    views: Schema.Attribute.BigInteger;
    x_id: Schema.Attribute.String & Schema.Attribute.Unique;
  };
}

export interface ApiSocialLogSocialLog extends Struct.CollectionTypeSchema {
  collectionName: 'social_logs';
  info: {
    description: '';
    displayName: 'Social Log';
    pluralName: 'social-logs';
    singularName: 'social-log';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    affiliate: Schema.Attribute.Relation<'manyToOne', 'api::affiliate.affiliate'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    fetched_at: Schema.Attribute.DateTime;
    is_empty_result: Schema.Attribute.Boolean;
    keyword: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::social-log.social-log'> &
      Schema.Attribute.Private;
    platform: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiSubscriptionTierSubscriptionTier extends Struct.CollectionTypeSchema {
  collectionName: 'subscription_tiers';
  info: {
    description: 'Define different subscription tiers with pricing and features';
    displayName: 'Subscription Tier';
    pluralName: 'subscription-tiers';
    singularName: 'subscription-tier';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    description: Schema.Attribute.Text;
    display_name: Schema.Attribute.String & Schema.Attribute.Required;
    duration_days: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      > &
      Schema.Attribute.DefaultTo<30>;
    features: Schema.Attribute.JSON;
    is_active: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
    is_popular: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::subscription-tier.subscription-tier'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required & Schema.Attribute.Unique;
    price: Schema.Attribute.Decimal &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    publishedAt: Schema.Attribute.DateTime;
    request_limit: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      > &
      Schema.Attribute.DefaultTo<100>;
    save_percent: Schema.Attribute.Decimal &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    stripe_metadata: Schema.Attribute.JSON;
    stripe_price_id: Schema.Attribute.String;
    stripe_product_id: Schema.Attribute.String;
    stripe_recurring_interval: Schema.Attribute.Enumeration<['month', 'year', 'quarter']> &
      Schema.Attribute.DefaultTo<'month'>;
    tier_features: Schema.Attribute.Component<'shared.listing', true>;
    traffic_share_rate: Schema.Attribute.Decimal;
    transactions: Schema.Attribute.Relation<'oneToMany', 'api::transaction.transaction'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    users: Schema.Attribute.Relation<'oneToMany', 'plugin::users-permissions.user'>;
  };
}

export interface ApiTagTag extends Struct.CollectionTypeSchema {
  collectionName: 'tags';
  info: {
    description: '';
    displayName: 'Tag';
    pluralName: 'tags';
    singularName: 'tag';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    affiliates: Schema.Attribute.Relation<'manyToMany', 'api::affiliate.affiliate'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    icon: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::tag.tag'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiTopAdLogTopAdLog extends Struct.CollectionTypeSchema {
  collectionName: 'top_ad_logs';
  info: {
    description: '';
    displayName: 'Top Ad Log';
    pluralName: 'top-ad-logs';
    singularName: 'top-ad-log';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    affiliate: Schema.Attribute.Relation<'oneToOne', 'api::affiliate.affiliate'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    keyword: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::top-ad-log.top-ad-log'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    result: Schema.Attribute.JSON;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface ApiTopAdTopAd extends Struct.CollectionTypeSchema {
  collectionName: 'top_ads';
  info: {
    description: '';
    displayName: 'Top Ad';
    pluralName: 'top-ads';
    singularName: 'top-ad';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    ad_id: Schema.Attribute.String & Schema.Attribute.Required & Schema.Attribute.Unique;
    ad_title: Schema.Attribute.Text;
    affiliate: Schema.Attribute.Relation<'oneToOne', 'api::affiliate.affiliate'>;
    brand_name: Schema.Attribute.String;
    comments: Schema.Attribute.Integer;
    cost: Schema.Attribute.Integer;
    country_code: Schema.Attribute.JSON;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    ctr: Schema.Attribute.Decimal;
    favorite: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    industry_key: Schema.Attribute.String;
    is_displayed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
    is_search: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    keyword: Schema.Attribute.String;
    landing_page: Schema.Attribute.Text;
    last_fetched: Schema.Attribute.DateTime;
    likes: Schema.Attribute.Integer;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::top-ad.top-ad'> &
      Schema.Attribute.Private;
    objective_key: Schema.Attribute.String;
    objectives: Schema.Attribute.JSON;
    platform: Schema.Attribute.Enumeration<['tiktok', 'youtube']>;
    published_from: Schema.Attribute.DateTime;
    publishedAt: Schema.Attribute.DateTime;
    shares: Schema.Attribute.Integer;
    source: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    video_info: Schema.Attribute.JSON;
    views: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
  };
}

export interface ApiTrackLinkTrackLink extends Struct.CollectionTypeSchema {
  collectionName: 'track_links';
  info: {
    description: '';
    displayName: 'Track Link';
    pluralName: 'track-links';
    singularName: 'track-link';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::track-link.track-link'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    referrer_link: Schema.Attribute.Relation<'manyToOne', 'api::referrer-link.referrer-link'>;
    referrer_url: Schema.Attribute.String;
    type: Schema.Attribute.Enumeration<['visitors', 'leads', 'conversions']>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    user: Schema.Attribute.Relation<'oneToOne', 'plugin::users-permissions.user'>;
  };
}

export interface ApiTrafficWebTrafficWeb extends Struct.CollectionTypeSchema {
  collectionName: 'traffic_webs';
  info: {
    description: '';
    displayName: 'Traffic Web';
    pluralName: 'traffic-webs';
    singularName: 'traffic-web';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    affiliate: Schema.Attribute.Relation<'manyToOne', 'api::affiliate.affiliate'>;
    bounce_rate: Schema.Attribute.Decimal;
    category_rank: Schema.Attribute.JSON;
    chart: Schema.Attribute.JSON;
    country_rank: Schema.Attribute.JSON;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    global_rank: Schema.Attribute.Integer;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::traffic-web.traffic-web'> &
      Schema.Attribute.Private;
    month: Schema.Attribute.Integer;
    page_per_visit: Schema.Attribute.Decimal;
    publishedAt: Schema.Attribute.DateTime;
    summary_keywords: Schema.Attribute.JSON;
    time_on_site: Schema.Attribute.Decimal;
    top_countries: Schema.Attribute.JSON;
    top_keyword: Schema.Attribute.JSON;
    traffic_sources: Schema.Attribute.JSON;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    url: Schema.Attribute.Text;
    visits: Schema.Attribute.Integer;
    year: Schema.Attribute.Integer;
  };
}

export interface ApiTransactionTransaction extends Struct.CollectionTypeSchema {
  collectionName: 'transactions';
  info: {
    description: 'Record of subscription purchases';
    displayName: 'Transaction';
    pluralName: 'transactions';
    singularName: 'transaction';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    amount: Schema.Attribute.Decimal &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      >;
    auto_renew: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
    cancellation_date: Schema.Attribute.DateTime;
    cancellation_reason: Schema.Attribute.String;
    child_transactions: Schema.Attribute.Relation<'oneToMany', 'api::transaction.transaction'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    currency: Schema.Attribute.Enumeration<['USD', 'EUR']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'USD'>;
    current_period_end: Schema.Attribute.DateTime;
    current_period_start: Schema.Attribute.DateTime;
    is_checked_subscription: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::transaction.transaction'> &
      Schema.Attribute.Private;
    parent_transaction: Schema.Attribute.Relation<'manyToOne', 'api::transaction.transaction'>;
    payment_details: Schema.Attribute.JSON;
    payment_method: Schema.Attribute.Enumeration<['stripe', 'free']>;
    payment_status: Schema.Attribute.Enumeration<
      ['pending', 'completed', 'failed', 'refunded', 'cancelled', 'past_due', 'unpaid']
    > &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'pending'>;
    publishedAt: Schema.Attribute.DateTime;
    stripe_checkout_session: Schema.Attribute.String;
    stripe_customer_id: Schema.Attribute.String;
    stripe_invoice_id: Schema.Attribute.String;
    stripe_price_id: Schema.Attribute.String;
    stripe_subscription_id: Schema.Attribute.String;
    subscription_tier: Schema.Attribute.Relation<
      'manyToOne',
      'api::subscription-tier.subscription-tier'
    >;
    transaction_date: Schema.Attribute.DateTime & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    user: Schema.Attribute.Relation<'oneToOne', 'plugin::users-permissions.user'>;
  };
}

export interface ApiUserTrackingRequestUserTrackingRequest extends Struct.CollectionTypeSchema {
  collectionName: 'user_tracking_requests';
  info: {
    description: 'Track and limit user requests';
    displayName: 'User Tracking Request';
    pluralName: 'user-tracking-requests';
    singularName: 'user-tracking-request';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    current_period_end: Schema.Attribute.DateTime;
    daily_request_count: Schema.Attribute.Integer &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      > &
      Schema.Attribute.DefaultTo<0>;
    daily_request_limit: Schema.Attribute.Integer &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      > &
      Schema.Attribute.DefaultTo<0>;
    last_daily_reset_date: Schema.Attribute.DateTime;
    last_request_date: Schema.Attribute.DateTime;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::user-tracking-request.user-tracking-request'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    request_count: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 0;
        },
        number
      > &
      Schema.Attribute.DefaultTo<0>;
    request_limit: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          min: 1;
        },
        number
      > &
      Schema.Attribute.DefaultTo<200>;
    statistics: Schema.Attribute.JSON;
    subscription_tier: Schema.Attribute.Relation<
      'manyToOne',
      'api::subscription-tier.subscription-tier'
    >;
    transaction: Schema.Attribute.Relation<'oneToOne', 'api::transaction.transaction'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    users_permissions_user: Schema.Attribute.Relation<'oneToOne', 'plugin::users-permissions.user'>;
  };
}

export interface PluginContentReleasesRelease extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_releases';
  info: {
    displayName: 'Release';
    pluralName: 'releases';
    singularName: 'release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    actions: Schema.Attribute.Relation<'oneToMany', 'plugin::content-releases.release-action'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'plugin::content-releases.release'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    releasedAt: Schema.Attribute.DateTime;
    scheduledAt: Schema.Attribute.DateTime;
    status: Schema.Attribute.Enumeration<['ready', 'blocked', 'failed', 'done', 'empty']> &
      Schema.Attribute.Required;
    timezone: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_release_actions';
  info: {
    displayName: 'Release Action';
    pluralName: 'release-actions';
    singularName: 'release-action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentType: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    entryDocumentId: Schema.Attribute.String;
    isEntryValid: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    release: Schema.Attribute.Relation<'manyToOne', 'plugin::content-releases.release'>;
    type: Schema.Attribute.Enumeration<['publish', 'unpublish']> & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface PluginI18NLocale extends Struct.CollectionTypeSchema {
  collectionName: 'i18n_locale';
  info: {
    collectionName: 'locales';
    description: '';
    displayName: 'Locale';
    pluralName: 'locales';
    singularName: 'locale';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'plugin::i18n.locale'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.SetMinMax<
        {
          max: 50;
          min: 1;
        },
        number
      >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface PluginReviewWorkflowsWorkflow extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows';
  info: {
    description: '';
    displayName: 'Workflow';
    name: 'Workflow';
    pluralName: 'workflows';
    singularName: 'workflow';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentTypes: Schema.Attribute.JSON &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'[]'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'plugin::review-workflows.workflow'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required & Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    stageRequiredToPublish: Schema.Attribute.Relation<
      'oneToOne',
      'plugin::review-workflows.workflow-stage'
    >;
    stages: Schema.Attribute.Relation<'oneToMany', 'plugin::review-workflows.workflow-stage'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface PluginReviewWorkflowsWorkflowStage extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows_stages';
  info: {
    description: '';
    displayName: 'Stages';
    name: 'Workflow Stage';
    pluralName: 'workflow-stages';
    singularName: 'workflow-stage';
  };
  options: {
    draftAndPublish: false;
    version: '1.1.0';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    color: Schema.Attribute.String & Schema.Attribute.DefaultTo<'#4945FF'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    permissions: Schema.Attribute.Relation<'manyToMany', 'admin::permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    workflow: Schema.Attribute.Relation<'manyToOne', 'plugin::review-workflows.workflow'>;
  };
}

export interface PluginUploadFile extends Struct.CollectionTypeSchema {
  collectionName: 'files';
  info: {
    description: '';
    displayName: 'File';
    pluralName: 'files';
    singularName: 'file';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    alternativeText: Schema.Attribute.String;
    caption: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    ext: Schema.Attribute.String;
    folder: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'> &
      Schema.Attribute.Private;
    folderPath: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    formats: Schema.Attribute.JSON;
    hash: Schema.Attribute.String & Schema.Attribute.Required;
    height: Schema.Attribute.Integer;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.file'> &
      Schema.Attribute.Private;
    mime: Schema.Attribute.String & Schema.Attribute.Required;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    previewUrl: Schema.Attribute.String;
    provider: Schema.Attribute.String & Schema.Attribute.Required;
    provider_metadata: Schema.Attribute.JSON;
    publishedAt: Schema.Attribute.DateTime;
    related: Schema.Attribute.Relation<'morphToMany'>;
    size: Schema.Attribute.Decimal & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    url: Schema.Attribute.String & Schema.Attribute.Required;
    width: Schema.Attribute.Integer;
  };
}

export interface PluginUploadFolder extends Struct.CollectionTypeSchema {
  collectionName: 'upload_folders';
  info: {
    displayName: 'Folder';
    pluralName: 'folders';
    singularName: 'folder';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    children: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.folder'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    files: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.file'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.folder'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    parent: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'>;
    path: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    pathId: Schema.Attribute.Integer & Schema.Attribute.Required & Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission extends Struct.CollectionTypeSchema {
  collectionName: 'up_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'plugin::users-permissions.permission'> &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<'manyToOne', 'plugin::users-permissions.role'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole extends Struct.CollectionTypeSchema {
  collectionName: 'up_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'role';
    pluralName: 'roles';
    singularName: 'role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'plugin::users-permissions.role'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    permissions: Schema.Attribute.Relation<'oneToMany', 'plugin::users-permissions.permission'>;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.String & Schema.Attribute.Unique;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    users: Schema.Attribute.Relation<'oneToMany', 'plugin::users-permissions.user'>;
  };
}

export interface PluginUsersPermissionsUser extends Struct.CollectionTypeSchema {
  collectionName: 'up_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'user';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    address: Schema.Attribute.String;
    apt: Schema.Attribute.String;
    bank_transfer: Schema.Attribute.Component<'payment.bank-details', false>;
    blocked: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    city: Schema.Attribute.String;
    confirmationToken: Schema.Attribute.String & Schema.Attribute.Private;
    confirmed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    country: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    first_name: Schema.Attribute.String;
    last_name: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'plugin::users-permissions.user'> &
      Schema.Attribute.Private;
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    paypal_email: Schema.Attribute.Email;
    provider: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    referral: Schema.Attribute.Relation<'oneToOne', 'api::referral.referral'>;
    referrer: Schema.Attribute.Relation<'oneToOne', 'api::referrer.referrer'>;
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
    role: Schema.Attribute.Relation<'manyToOne', 'plugin::users-permissions.role'>;
    state: Schema.Attribute.String;
    stripe_customer_id: Schema.Attribute.String;
    subscription_tier: Schema.Attribute.Relation<
      'manyToOne',
      'api::subscription-tier.subscription-tier'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> & Schema.Attribute.Private;
    user_tracking_request: Schema.Attribute.Relation<
      'oneToOne',
      'api::user-tracking-request.user-tracking-request'
    >;
    username: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    zip_code: Schema.Attribute.String;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ContentTypeSchemas {
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::permission': AdminPermission;
      'admin::role': AdminRole;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'admin::user': AdminUser;
      'api::about.about': ApiAboutAbout;
      'api::affiliate.affiliate': ApiAffiliateAffiliate;
      'api::airtable.airtable': ApiAirtableAirtable;
      'api::aiscript-session.aiscript-session': ApiAiscriptSessionAiscriptSession;
      'api::aiscript.aiscript': ApiAiscriptAiscript;
      'api::category.category': ApiCategoryCategory;
      'api::chat-message.chat-message': ApiChatMessageChatMessage;
      'api::chat-session.chat-session': ApiChatSessionChatSession;
      'api::commission-config.commission-config': ApiCommissionConfigCommissionConfig;
      'api::commission.commission': ApiCommissionCommission;
      'api::global.global': ApiGlobalGlobal;
      'api::industry.industry': ApiIndustryIndustry;
      'api::keyword.keyword': ApiKeywordKeyword;
      'api::page.page': ApiPagePage;
      'api::payment-method.payment-method': ApiPaymentMethodPaymentMethod;
      'api::payout.payout': ApiPayoutPayout;
      'api::prompt.prompt': ApiPromptPrompt;
      'api::referral-activity.referral-activity': ApiReferralActivityReferralActivity;
      'api::referral-commission.referral-commission': ApiReferralCommissionReferralCommission;
      'api::referral.referral': ApiReferralReferral;
      'api::referrer-link.referrer-link': ApiReferrerLinkReferrerLink;
      'api::referrer.referrer': ApiReferrerReferrer;
      'api::search-keyword.search-keyword': ApiSearchKeywordSearchKeyword;
      'api::social-listening.social-listening': ApiSocialListeningSocialListening;
      'api::social-log.social-log': ApiSocialLogSocialLog;
      'api::subscription-tier.subscription-tier': ApiSubscriptionTierSubscriptionTier;
      'api::tag.tag': ApiTagTag;
      'api::top-ad-log.top-ad-log': ApiTopAdLogTopAdLog;
      'api::top-ad.top-ad': ApiTopAdTopAd;
      'api::track-link.track-link': ApiTrackLinkTrackLink;
      'api::traffic-web.traffic-web': ApiTrafficWebTrafficWeb;
      'api::transaction.transaction': ApiTransactionTransaction;
      'api::user-tracking-request.user-tracking-request': ApiUserTrackingRequestUserTrackingRequest;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::review-workflows.workflow': PluginReviewWorkflowsWorkflow;
      'plugin::review-workflows.workflow-stage': PluginReviewWorkflowsWorkflowStage;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
    }
  }
}
