"use client";
import { useDispatch, useSelector } from "react-redux";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { useEffect, useState, useRef } from "react";
import { ProfileModal } from "@/components";
import router from "next/router";
import {
  affiliateActions,
  categoryActions,
  paymentMethodActions,
} from "@/features/rootActions";
import { NavDrawer } from "./NavDrawer";
import LinkHeader from "./LinkHeader";
import SearchNav from "./SearchNav";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { Button } from "../ui/button";
import {
  selectCategories,
  selectPaymentMethods,
} from "@/features/selectors";
import {
  selectIsAuthenticated,
} from "@/features/auth/auth.slice";
import { selectAffiliateUrl } from "@/features/affiliate/affiliate.slice";
import {
  actions as discourseActions,
  selectDiscourseLoading
} from "@/features/discourse/discourse.slice";
import Link from "next/link";

export default function Header() {
  const dispatch = useDispatch();
  const categories = useSelector(selectCategories);
  const paymentMethods = useSelector(selectPaymentMethods);
  const isAuth = useSelector(selectIsAuthenticated);
  const affiliateUrl = useSelector(selectAffiliateUrl);
  const discourseLoading = useSelector(selectDiscourseLoading);
  const [activeNav, setActiveNav] = useState("Home");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const isOpeningRef = useRef(false);

  // Fix hydration mismatch by ensuring client-side rendering for auth-dependent content
  useEffect(() => {
    setMounted(true);
  }, []);

  const { fetchAll: fetchCategories } = categoryActions;
  const { fetchPaymentMethods } = paymentMethodActions;

  useEffect(() => {
    // check if the categories are already fetched
    if (categories.length > 1) return;
    dispatch(fetchCategories());

    if (paymentMethods && paymentMethods.length > 0) return;
    dispatch(fetchPaymentMethods());
  }, [dispatch]);

  // Add effect to handle URL opening when it's fetched
  useEffect(() => {
    if (affiliateUrl && !isOpeningRef.current) {
      console.log("Opening affiliate URL:", affiliateUrl);
      isOpeningRef.current = true;

      // Function to try opening in new tab, fallback to current tab if blocked
      const openUrlSafely = (url: string) => {
        // Try direct window.open first
        const newWindow = window.open(url, "_blank");

        // Use a timeout to check if the window actually opened
        setTimeout(() => {
          let windowBlocked = false;

          try {
            if (
              !newWindow ||
              newWindow.closed ||
              typeof newWindow.closed === "undefined"
            ) {
              windowBlocked = true;
            }
          } catch (e) {
            windowBlocked = true;
          }

          if (windowBlocked) {
            console.log("window.open was blocked, trying alternative method");

            // Fallback: Create a temporary link and trigger click
            const a = document.createElement("a");
            a.href = url;
            a.target = "_blank";
            a.rel = "noopener noreferrer";
            a.style.position = "absolute";
            a.style.left = "-9999px";

            document.body.appendChild(a);

            try {
              a.click();
              console.log("Link click method used");
            } catch (error) {
              console.log(
                "Link click failed, redirecting in current tab",
                error
              );
              window.location.href = url;
            } finally {
              document.body.removeChild(a);
            }
          } else {
            console.log("New tab opened successfully via window.open");
            if (newWindow) {
              newWindow.focus();
            }
          }
        }, 100);
      };

      // Use the safe opening approach
      openUrlSafely(affiliateUrl);

      // Immediately clear the URL from state after opening
      dispatch(affiliateActions.setAffiliateUrl(null));

      // Reset the flag after a short delay
      setTimeout(() => {
        isOpeningRef.current = false;
      }, 500);
    }
  }, [affiliateUrl, dispatch]);

  // Handle community button click
  const handleCommunityClick = () => {
    if (!isAuth) {
      // If not authenticated, redirect to login
      router.push("/authentication");
      return;
    }

    // Dispatch action to get Discourse SSO URL
    dispatch(discourseActions.getDiscourseSSOUrl());
  };

  return (
    <header className="shadow-md fixed top-0 left-0 right-0 z-50 bg-primary">
      <div className="mx-auto lg:px-[50px] px-[20px] py-[15px] flex items-center justify-between border-b border-[rgba(56,97,251,0.05)]">
        <div className="logo flex items-center gap-4">
          <h1
            onClick={() => {
              setActiveNav("Home");
              router.push("/");
            }}
            className="text-[28px] font-bold text-[#3861FB] cursor-pointer"
          >
            Affitor
          </h1>
          <LinkHeader
            label="Home"
            href="/"
            activeNav={activeNav}
            setActiveNav={setActiveNav}
          />
        </div>

        <nav className="hidden lg:flex items-center gap-6">
          {/* Remove Home from here, now next to logo */}

          <SearchNav />

          <LinkHeader
            label="Top Videos"
            href="/top-videos"
            activeNav={activeNav}
            setActiveNav={setActiveNav}
          />

          <LinkHeader
            label="Affitor Hunt"
            href="/hunt"
            activeNav={activeNav}
            setActiveNav={setActiveNav}
          />

          {/*
          <div className="relative group z-10">
            <LinkHeader
              label="Categories"
              href="/"
              activeNav={activeNav}
              isDropdown={true}
            >
              <>
                Categories
                <div
                  className="invisible opacity-0 group-hover:visible group-hover:opacity-100 absolute top-full left-0 w-48 rounded-lg shadow-lg 
                 bg-primary transform transition-all duration-300 ease-in-out translate-y-4 group-hover:translate-y-0"
                >
                  <CategorySelector categories={categories} />
                </div>
              </>
            </LinkHeader>
          </div>
          */}
          <LinkHeader
            label="Pricing"
            href="/pricing"
            activeNav={activeNav}
            setActiveNav={setActiveNav}
          />

          {/* Community button for authenticated users */}
          {mounted && isAuth && (
            <button
              onClick={handleCommunityClick}
              disabled={discourseLoading}
              className={`
                px-5 py-2 rounded-lg font-semibold shadow transition-all duration-200 flex items-center gap-2
                ${discourseLoading
                  ? 'bg-blue-500 text-white cursor-not-allowed opacity-75'
                  : 'bg-blue-600 text-white hover:bg-blue-700 hover:shadow-lg active:bg-blue-800'
                }
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                transform hover:scale-105 active:scale-95
              `}
              aria-label={discourseLoading ? "Connecting to Community" : "Join Community"}
            >
              {discourseLoading ? (
                <>
                  {/* Loading spinner */}
                  <svg
                    className="animate-spin h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Connecting...
                </>
              ) : (
                <>
                  <i className="fas fa-users"></i>
                  Community
                </>
              )}
            </button>
          )}

          {/* Prominent Sign In button */}
          {mounted && !isAuth && (
            <Link
              href="/authentication"
              className="bg-blue-600 text-white px-5 py-2 rounded-lg font-semibold shadow transition-all duration-200 hover:bg-blue-700 hover:shadow-lg active:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:scale-105 active:scale-95 flex items-center gap-2"
              onClick={() => setActiveNav("Sign In")}
              aria-label="Sign In"
            >
              <i className="fas fa-sign-in-alt"></i>
              Sign In
            </Link>
          )}
          {/* Profile button for authenticated users */}
          {mounted && isAuth && (
            <ProfileModal
              isOpen={isProfileModalOpen}
              onClose={() => setIsProfileModalOpen(false)}
            >
              <button
                onClick={() => setIsProfileModalOpen(true)}
                className="text-primary-foreground hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium"
              >
                Profile
              </button>
            </ProfileModal>
          )}
        </nav>

        <div className="lg:hidden flex items-center">
          <style>
            {`
              .search-content .lucide.lucide-x {
                display: none
              }
            `}
          </style>
          <Dialog>
            <DialogTrigger asChild>
              <Button className="text-xl bg-transparent shadow-none">
                <i className="fas fa-search"></i>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px] top-[100px] bg-transparent shadow-none border-none py-0 search-content">
              <DialogTitle asChild>
                <VisuallyHidden>Dialog Title Here</VisuallyHidden>
              </DialogTitle>
              <SearchNav />
            </DialogContent>
          </Dialog>
          <NavDrawer
            categories={categories}
            activeNav={activeNav}
          >
            <button
              className="text-2xl"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <i className="fas fa-bars"></i>
            </button>
          </NavDrawer>
        </div>
      </div>


    </header>
  );
}
